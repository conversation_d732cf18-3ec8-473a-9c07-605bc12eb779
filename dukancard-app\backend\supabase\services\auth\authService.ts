import { supabase } from '@/lib/supabase';

export class AuthService {
  static async getCurrentUser() {
    try {
      const result = await supabase.auth.getUser();

      // Handle specific auth errors
      if (result.error) {
        const error = result.error;
        if (error.message?.includes('User from sub claim in JWT does not exist') ||
            error.message?.includes('session_not_found') ||
            error.message?.includes('invalid_token') ||
            error.code === 'session_not_found') {
          // Clear invalid session
          await supabase.auth.signOut();
        }
      }

      return result;
    } catch (error) {
      console.error('AuthService.getCurrentUser error:', error);

      // Handle unexpected errors
      if (error instanceof Error &&
          (error.message?.includes('User from sub claim in JWT does not exist') ||
           error.message?.includes('session_not_found') ||
           error.message?.includes('invalid_token'))) {
        // Clear invalid session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error('Error signing out:', signOutError);
        }
      }

      throw error;
    }
  }

  static async getCustomerProfile(userId: string) {
    return await supabase
      .from('customer_profiles')
      .select('*, latitude, longitude')
      .eq('id', userId)
      .single();
  }

  static async hasCustomerProfile(userId: string) {
    const { data, error } = await supabase
      .from('customer_profiles')
      .select('id')
      .eq('id', userId)
      .single();
    return !error && !!data;
  }

  static async hasBusinessProfile(userId: string) {
    const { data, error } = await supabase
      .from('business_profiles')
      .select('id')
      .eq('id', userId)
      .single();
    return !error && !!data;
  }

  static async getCustomerProfileForValidation(userId: string) {
    return await supabase
      .from('customer_profiles')
      .select('name, pincode, state, city, locality, address, latitude, longitude')
      .eq('id', userId)
      .single();
  }

  static async signOut() {
    return await supabase.auth.signOut();
  }

  static async signInWithMobilePassword(mobile: string, password: string) {
    return await supabase.auth.signInWithPassword({
      phone: mobile,
      password: password,
    });
  }
}