import { router } from 'expo-router';
import { supabase } from '../supabase';
import { getAddressValidationMessage, getMissingAddressFields, isCustomerAddressComplete, type CustomerAddressData } from '@/backend/supabase/utils/addressValidation';
import { AuthService } from '@/backend/supabase/services/auth/authService';

/**
 * Customer authentication utilities for React Native
 * Based on dukancard/lib/actions/customerProfiles/addressValidation.ts
 */

export interface AuthUser {
  id: string;
  email?: string;
  phone?: string;
  user_metadata?: any;
}

export interface CustomerProfile {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  pincode?: string;
  state?: string;
  city?: string;
  locality?: string;
  address?: string;
  latitude?: number;
  longitude?: number;
  avatar_url?: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get current authenticated user
 */
export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    const { data: { user }, error } = await AuthService.getCurrentUser();

    if (error) {
      console.error('Error getting current user:', error);

      // Handle specific auth errors that require redirect to login
      if (error.message?.includes('User from sub claim in JWT does not exist') ||
          error.message?.includes('session_not_found') ||
          error.message?.includes('invalid_token') ||
          error.message?.includes('jwt') ||
          error.code === 'session_not_found') {
        console.log('Auth error detected, redirecting to login:', error.message);
        // Clear any invalid session
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error('Error signing out:', signOutError);
        }
        // Redirect to login
        router.replace('/(auth)/login');
      }

      return null;
    }

    return user;
  } catch (error) {
    console.error('Unexpected error getting current user:', error);

    // Handle unexpected errors that might be auth-related
    if (error instanceof Error &&
        (error.message?.includes('User from sub claim in JWT does not exist') ||
         error.message?.includes('session_not_found') ||
         error.message?.includes('invalid_token') ||
         error.message?.includes('jwt'))) {
      console.log('Unexpected auth error detected, redirecting to login:', error.message);
      // Clear any invalid session
      try {
        await supabase.auth.signOut();
      } catch (signOutError) {
        console.error('Error signing out:', signOutError);
      }
      // Redirect to login
      router.replace('/(auth)/login');
    }

    return null;
  }
}

/**
 * Get customer profile data
 */
export async function getCustomerProfile(userId: string): Promise<{
  data?: CustomerProfile;
  error?: string;
}> {
  try {
    const { data: profile, error } = await AuthService.getCustomerProfile(userId);
    
    if (error) {
      console.error('Error fetching customer profile:', error);
      return { error: 'Failed to fetch customer profile' };
    }
    
    return { data: profile };
  } catch (error) {
    console.error('Unexpected error fetching customer profile:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Check if user has a customer profile
 */
export async function hasCustomerProfile(userId: string): Promise<boolean> {
  try {
    return await AuthService.hasCustomerProfile(userId);
  } catch (error) {
    console.error('Error checking customer profile:', error);
    return false;
  }
}

/**
 * Check if user has a business profile
 */
export async function hasBusinessProfile(userId: string): Promise<boolean> {
  try {
    return await AuthService.hasBusinessProfile(userId);
  } catch (error) {
    console.error('Error checking business profile:', error);
    return false;
  }
}

/**
 * Determine user type based on profiles
 */
export async function getUserType(userId: string): Promise<'customer' | 'business' | null> {
  try {
    const [hasCustomer, hasBusiness] = await Promise.all([
      hasCustomerProfile(userId),
      hasBusinessProfile(userId)
    ]);
    
    if (hasCustomer) return 'customer';
    if (hasBusiness) return 'business';
    return null;
  } catch (error) {
    console.error('Error determining user type:', error);
    return null;
  }
}

/**
 * Validates if customer profile is complete (name and address)
 * Returns validation result and navigation path if needed
 */
export async function validateCustomerProfile(userId: string): Promise<{
  isValid: boolean;
  missingFields?: string[];
  message?: string;
  navigationPath?: string;
}> {
  try {
    // Fetch customer profile data including name and address
    const { data: profile, error } = await AuthService.getCustomerProfileForValidation(userId);

    if (error) {
      console.error('Error fetching customer profile for validation:', error);
      // If we can't fetch the profile, assume invalid and redirect
      return {
        isValid: false,
        message: 'Unable to verify your profile information. Please complete your profile.',
        navigationPath: '/(auth)/complete-profile'
      };
    }

    const missingFields: string[] = [];

    // Check if name is missing
    if (!profile?.name || profile.name.trim() === '') {
      missingFields.push('name');
    }

    // Check address fields
    const addressData: CustomerAddressData = {
      pincode: profile?.pincode,
      state: profile?.state,
      city: profile?.city,
      locality: profile?.locality,
      address: profile?.address
    };

    const isAddressComplete = isCustomerAddressComplete(addressData);
    if (!isAddressComplete) {
      const addressMissingFields = getMissingAddressFields(addressData);
      missingFields.push(...addressMissingFields);
    }

    if (missingFields.length > 0) {
      const message = `Please complete your profile. Missing: ${missingFields.join(', ')}`;
      const navigationPath = '/(auth)/complete-profile';

      return {
        isValid: false,
        missingFields,
        message,
        navigationPath
      };
    }

    return { isValid: true };

  } catch (error) {
    console.error('Unexpected error during profile validation:', error);
    return {
      isValid: false,
      message: 'An error occurred while validating your profile. Please complete your profile.',
      navigationPath: '/(auth)/complete-profile'
    };
  }
}

/**
 * Validates if customer has complete address information
 * Returns validation result and navigation path if needed
 */
export async function validateCustomerAddress(userId: string): Promise<{
  isValid: boolean;
  missingFields?: string[];
  message?: string;
  navigationPath?: string;
}> {
  try {
    // Fetch customer address data
    const { data: profile, error } = await AuthService.getCustomerProfileForValidation(userId);
    
    if (error) {
      console.error('Error fetching customer profile for address validation:', error);
      // If we can't fetch the profile, assume invalid and redirect
      return {
        isValid: false,
        message: 'Unable to verify your address information. Please complete your profile.',
        navigationPath: '/(auth)/complete-profile'
      };
    }
    
    const addressData: CustomerAddressData = {
      pincode: profile?.pincode,
      state: profile?.state,
      city: profile?.city,
      locality: profile?.locality,
      address: profile?.address
    };
    
    const isValid = isCustomerAddressComplete(addressData);
    
    if (!isValid) {
      const missingFields = getMissingAddressFields(addressData);
      const message = getAddressValidationMessage(missingFields);
      const navigationPath = '/(auth)/complete-profile';
      
      return {
        isValid: false,
        missingFields,
        message,
        navigationPath
      };
    }
    
    return { isValid: true };
    
  } catch (error) {
    console.error('Unexpected error during address validation:', error);
    return {
      isValid: false,
      message: 'An error occurred while validating your address. Please complete your profile.',
      navigationPath: '/(auth)/complete-profile'
    };
  }
}

/**
 * Check address completion status
 * Use this in customer dashboard screens
 */
export async function requireCompleteAddress(userId: string): Promise<boolean> {
  const validation = await validateCustomerAddress(userId);
  return validation.isValid;
}

/**
 * Check complete profile status (name and address)
 * Use this in customer dashboard screens
 */
export async function requireCompleteCustomerProfile(userId: string): Promise<boolean> {
  const validation = await validateCustomerProfile(userId);
  return validation.isValid;
}

/**
 * Check profile completion and navigate if incomplete
 * Use this in customer dashboard screens (except settings page)
 * Settings page is exempt from address validation
 */
export async function requireCompleteProfile(userId: string, exemptFromAddressValidation: boolean = false): Promise<boolean> {
  // Only check address if not exempt (settings page is exempt)
  if (!exemptFromAddressValidation) {
    return await requireCompleteAddress(userId);
  }

  return true;
}

/**
 * Get customer address data for forms
 */
export async function getCustomerAddressData(userId: string): Promise<{
  data?: CustomerAddressData;
  error?: string;
}> {
  try {
    const { data: profile, error } = await AuthService.getCustomerProfileForValidation(userId);
    
    if (error) {
      console.error('Error fetching customer address data:', error);
      return { error: 'Failed to fetch address data' };
    }
    
    return {
      data: {
        pincode: profile?.pincode,
        state: profile?.state,
        city: profile?.city,
        locality: profile?.locality,
        address: profile?.address
      }
    };
  } catch (error) {
    console.error('Unexpected error fetching customer address data:', error);
    return { error: 'An unexpected error occurred' };
  }
}

/**
 * Check if user is authenticated and has proper access
 */
export async function checkAuthAndAccess(): Promise<{
  isAuthenticated: boolean;
  user?: AuthUser;
  userType?: 'customer' | 'business' | null;
  shouldRedirect?: string;
}> {
  try {
    const user = await getCurrentUser();
    
    if (!user) {
      return {
        isAuthenticated: false,
        shouldRedirect: '/(auth)/login'
      };
    }
    
    const userType = await getUserType(user.id);
    
    if (!userType) {
      return {
        isAuthenticated: true,
        user,
        userType: null,
        shouldRedirect: '/(auth)/choose-role'
      };
    }
    
    return {
      isAuthenticated: true,
      user,
      userType
    };
  } catch (error) {
    console.error('Error checking auth and access:', error);
    return {
      isAuthenticated: false,
      shouldRedirect: '/(auth)/login'
    };
  }
}

/**
 * Sign out user
 */
export async function signOut(): Promise<{ error?: string }> {
  try {
    const { error } = await AuthService.signOut();
    
    if (error) {
      console.error('Error signing out:', error);
      return { error: 'Failed to sign out' };
    }
    
    // Navigate to login screen
    router.replace('/(auth)/login');
    return {};
  } catch (error) {
    console.error('Unexpected error signing out:', error);
    return { error: 'An unexpected error occurred' };
  }
}


