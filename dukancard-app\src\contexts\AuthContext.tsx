import { supabase } from '@/lib/supabase';
import { getUserRoleStatus } from '@/backend/supabase/services/common/userRoleStatusService';
import { Session, User } from '@supabase/supabase-js';
import React, { createContext, useContext, useEffect, useState } from 'react';

// Types for auth and profile status
export type AuthUser = User;
export interface UserRoleStatus {
  hasCustomerProfile: boolean;
  hasBusinessProfile: boolean;
  businessOnboardingCompleted: boolean;
  customerProfileComplete: boolean;
  role: 'customer' | 'business' | null;
  needsRoleSelection: boolean;
  needsOnboarding: boolean;
  needsProfileCompletion: boolean;
}

export interface ProfileStatus {
  loading: boolean;
  roleStatus: UserRoleStatus | null;
  error: any;
}

interface AuthContextType {
  user: AuthUser | null;
  session: Session | null;
  loading: boolean;
  profileStatus: ProfileStatus;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ error: any }>;
  checkUserRole: () => Promise<UserRoleStatus | null>;
  hasCompletedOnboarding: () => boolean;
  getUserProfile: () => Promise<any>;
  refreshProfileStatus: () => Promise<void>;
  forceRefreshAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Note: AppState listener for auto-refresh is now handled in lib/supabase.ts
// as part of the consolidated 2025 Supabase client configuration

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileStatus, setProfileStatus] = useState<ProfileStatus>({
    loading: false,
    roleStatus: null,
    error: null,
  });

  useEffect(() => {
    // Get initial session - following Supabase documentation pattern
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user as AuthUser || null);
      if (session?.user) {
        refreshProfileStatusForUser(session.user.id);
      }
      setLoading(false);
    });

    // Single auth state listener - following Supabase best practices
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      setSession(session);
      setUser(session?.user as AuthUser || null);

      // Handle different auth events
      if (event === 'SIGNED_OUT' || !session) {
        setProfileStatus({
          loading: false,
          roleStatus: null,
          error: null,
        });
        setLoading(false);
      } else if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
        if (session?.user) {
          await refreshProfileStatusForUser(session.user.id);
        }
        setLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  

  // Effect to refresh profile status when user changes
  useEffect(() => {
    const refreshStatus = async () => {
      if (user) {
        setProfileStatus(prev => ({ ...prev, loading: true }));

        try {
          const result = await getUserRoleStatus();
          if (!result.success) {
            setProfileStatus({
              loading: false,
              roleStatus: null,
              error: { message: result.error || 'Failed to get role status' },
            });
          } else {
            setProfileStatus({
              loading: false,
              roleStatus: result.data as UserRoleStatus,
              error: null,
            });
          }
        } catch (error) {
          console.error('AuthContext: Error refreshing profile status:', error);
          setProfileStatus({
            loading: false,
            roleStatus: null,
            error: { message: 'Failed to refresh profile status' },
          });
        }
      } else {
        setProfileStatus({
          loading: false,
          roleStatus: null,
          error: null,
        });
      }
    };

    refreshStatus();
  }, [user]);

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        // Handle specific error cases
        if (error.message.toLowerCase().includes('invalid login credentials')) {
          return {
            error: {
              message: 'Invalid email or password. Please check your credentials and try again.',
            },
          };
        }
        if (error.message.toLowerCase().includes('email not confirmed')) {
          return {
            error: {
              message: 'Please check your email and click the confirmation link before signing in.',
            },
          };
        }
        return { error };
      }

      return { error: null };
    } catch (err) {
      console.error('Unexpected error during login:', err);
      return {
        error: {
          message: 'An unexpected error occurred during login.',
        },
      };
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            name: fullName, // Also store as 'name' for compatibility
          },
        },
      });

      if (error) {
        // Handle specific error cases like in Dukancard
        if (error.message.toLowerCase().includes('user already registered')) {
          return {
            error: {
              message: 'An account with this email already exists. Please log in instead.',
            },
          };
        }
        return { error };
      }

      return { error: null };
    } catch (err) {
      console.error('Unexpected error during registration:', err);
      return {
        error: {
          message: 'An unexpected error occurred during registration.',
        },
      };
    }
  };

  const signOut = async () => {
    try {
      // First, sign out from Google to clear any cached sessions
      try {
        const { signOutFromGoogle } = await import('@/backend/supabase/services/auth/nativeGoogleAuth2025');
        await signOutFromGoogle();
      } catch (googleSignOutError) {
        // Ignore Google sign out errors - user might not be signed in with Google
        console.log('Google sign out (expected if not signed in with Google):', googleSignOutError);
      }

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (err) {
      return { error: err };
    }
  };

  const signInWithGoogle = async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: 'dukancardapp://auth/callback',
        },
      });
      return { error };
    } catch (err) {
      return { error: err };
    }
  };

  // New role and profile management functions
  const checkUserRole = async (): Promise<UserRoleStatus | null> => {
    if (!user) return null;

    try {
      const result = await getUserRoleStatus();
      if (!result.success) {
        return null;
      }
      return result.data as UserRoleStatus;
    } catch (error) {
      console.error('Error checking user role:', error);
      return null;
    }
  };

  const hasCompletedOnboarding = (): boolean => {
    return profileStatus.roleStatus?.businessOnboardingCompleted || false;
  };

  const getUserProfile = async () => {
    if (!user) return null;

    try {
      const roleStatus = await checkUserRole();
      if (!roleStatus) return null;

      if (roleStatus.hasCustomerProfile) {
        const { data, error } = await supabase
          .from('customer_profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        return error ? null : data;
      } else if (roleStatus.hasBusinessProfile) {
        const { data, error } = await supabase
          .from('business_profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        return error ? null : data;
      }

      return null;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  };

  // Helper function to refresh profile status for a specific user ID
  const refreshProfileStatusForUser = async (userId: string): Promise<void> => {
    setProfileStatus(prev => ({ ...prev, loading: true }));

    try {
      const result = await getUserRoleStatus();
      if (!result.success) {
        setProfileStatus({
          loading: false,
          roleStatus: null,
          error: { message: result.error || 'Failed to get role status' },
        });
      } else {
        setProfileStatus({
          loading: false,
          roleStatus: result.data as UserRoleStatus,
          error: null,
        });
      }
    } catch (error) {
      console.error('Error refreshing profile status:', error);
      setProfileStatus({
        loading: false,
        roleStatus: null,
        error: { message: 'Failed to refresh profile status' },
      });
    }
  };

  const refreshProfileStatus = async (): Promise<void> => {
    if (!user) {
      setProfileStatus({
        loading: false,
        roleStatus: null,
        error: null,
      });
      return;
    }

    await refreshProfileStatusForUser(user.id);
  };

  // Force refresh authentication state - validates session with server
  const forceRefreshAuth = async (): Promise<void> => {
    setLoading(true);

    try {
      // Get current session
      const { data: { session } } = await supabase.auth.getSession();

      if (session) {
        // Validate the session by calling getUser() which checks with server
        const { data: { user }, error } = await supabase.auth.getUser();

        if (error || !user) {
          // Session is invalid (user might be deleted), clear it
          console.log('Invalid session detected, clearing auth state:', error?.message);
          await supabase.auth.signOut();
          setSession(null);
          setUser(null);
          setProfileStatus({
            loading: false,
            roleStatus: null,
            error: null,
          });
        } else {
          // Session is valid, update state and refresh profile
          setSession(session);
          setUser(user as AuthUser);
          await refreshProfileStatusForUser(user.id);
        }
      } else {
        // No session
        setSession(null);
        setUser(null);
        setProfileStatus({
          loading: false,
          roleStatus: null,
          error: null,
        });
      }
    } catch (error) {
      console.error('Error force refreshing auth:', error);
      // On error, clear everything
      await supabase.auth.signOut();
      setSession(null);
      setUser(null);
      setProfileStatus({
        loading: false,
        roleStatus: null,
        error: null,
      });
    } finally {
      setLoading(false);
    }
  };

  const value = {
    user,
    session,
    loading,
    profileStatus,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    checkUserRole,
    hasCompletedOnboarding,
    getUserProfile,
    refreshProfileStatus,
    forceRefreshAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
